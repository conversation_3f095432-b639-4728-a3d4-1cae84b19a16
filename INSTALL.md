# 安装指南 - 增强版在线文件管理工具

## 🚀 快速开始

### 1. 检查PHP版本
首先上传 `check_php.php` 到您的服务器，通过浏览器访问来检查PHP版本：
```
http://您的域名/check_php.php
```

### 2. 选择合适的版本
根据检测结果选择合适的文件：
- **PHP 7.0+**: 使用 `unzip_enhanced.php`
- **PHP 5.4-6.x**: 使用 `unzip_compatible.php`

### 3. 上传文件
将选择的PHP文件上传到您的Web服务器目录。

### 4. 设置权限
确保PHP有读写当前目录的权限：
```bash
chmod 755 /path/to/your/directory
```

### 5. 访问工具
通过浏览器访问上传的PHP文件：
```
http://您的域名/unzip_enhanced.php
# 或
http://您的域名/unzip_compatible.php
```

## 📋 详细安装步骤

### 步骤1: 环境检查

#### 必需条件
- Web服务器 (Apache/Nginx/IIS)
- PHP 5.4+ (推荐 PHP 7.0+)
- 文件读写权限

#### 可选条件
- ZipArchive扩展 (用于ZIP解压功能)
- 足够的磁盘空间
- 合适的PHP配置

#### 检查ZipArchive扩展
创建一个PHP文件检查：
```php
<?php
if (class_exists('ZipArchive')) {
    echo "✅ ZipArchive扩展已安装";
} else {
    echo "❌ ZipArchive扩展未安装";
}
?>
```

### 步骤2: 文件上传

#### 方法1: FTP上传
1. 使用FTP客户端连接到服务器
2. 上传选择的PHP文件到网站根目录
3. 确保文件权限正确

#### 方法2: 控制面板上传
1. 登录您的主机控制面板
2. 进入文件管理器
3. 上传PHP文件到public_html或www目录

#### 方法3: 命令行上传
```bash
# 使用scp上传
scp unzip_compatible.php user@server:/path/to/web/directory/

# 或使用wget下载
wget https://your-domain.com/unzip_compatible.php
```

### 步骤3: 权限设置

#### Linux/Unix系统
```bash
# 设置目录权限
chmod 755 /path/to/directory

# 设置文件权限
chmod 644 unzip_compatible.php

# 如果需要执行权限
chmod 755 unzip_compatible.php
```

#### Windows系统
1. 右键点击文件夹
2. 选择"属性" → "安全"
3. 确保IIS_IUSRS有读写权限

### 步骤4: PHP配置优化

#### 推荐的php.ini设置
```ini
# 文件上传设置
upload_max_filesize = 100M
post_max_size = 100M
max_execution_time = 300
memory_limit = 256M

# 文件权限
allow_url_fopen = On
file_uploads = On

# 错误显示 (生产环境建议关闭)
display_errors = Off
log_errors = On
```

#### 检查当前配置
```php
<?php
echo "上传最大文件大小: " . ini_get('upload_max_filesize') . "<br>";
echo "POST最大大小: " . ini_get('post_max_size') . "<br>";
echo "最大执行时间: " . ini_get('max_execution_time') . "秒<br>";
echo "内存限制: " . ini_get('memory_limit') . "<br>";
?>
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. "Parse error: syntax error"
**原因**: PHP版本不兼容
**解决**: 使用 `unzip_compatible.php` 版本

#### 2. "Permission denied"
**原因**: 文件权限不足
**解决**: 
```bash
chmod 755 directory_name
chmod 644 file_name
```

#### 3. "ZipArchive not found"
**原因**: 缺少ZipArchive扩展
**解决**: 
- 联系主机商启用扩展
- 或使用其他解压方法

#### 4. "File upload failed"
**原因**: 上传配置限制
**解决**: 调整php.ini中的上传设置

#### 5. "Cannot create directory"
**原因**: 目录权限不足
**解决**: 
```bash
chmod 755 parent_directory
```

### 调试模式

#### 启用错误显示
在PHP文件开头添加：
```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
```

#### 检查文件权限
```php
<?php
$dir = './';
if (is_writable($dir)) {
    echo "✅ 目录可写";
} else {
    echo "❌ 目录不可写";
}
?>
```

## 🛡️ 安全建议

### 1. 访问控制
- 设置密码保护
- 限制IP访问
- 使用HTTPS

### 2. 文件保护
```php
// 在文件开头添加密码保护
<?php
session_start();
if (!isset($_SESSION['authenticated'])) {
    // 显示登录表单
    exit;
}
?>
```

### 3. 目录保护
创建 `.htaccess` 文件：
```apache
# 禁止直接访问敏感文件
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# 禁止目录浏览
Options -Indexes
```

### 4. 定期备份
- 定期备份重要文件
- 测试备份恢复
- 监控文件变化

## 📞 技术支持

### 获取帮助
1. 查看 `README.md` 文档
2. 检查常见问题解答
3. 查看错误日志
4. 联系技术支持

### 报告问题
提供以下信息：
- PHP版本
- 服务器类型
- 错误信息
- 操作步骤

### 更新说明
- 定期检查更新
- 备份后再更新
- 测试新功能

---

## 📝 安装检查清单

- [ ] 检查PHP版本
- [ ] 选择合适的文件版本
- [ ] 上传文件到服务器
- [ ] 设置正确的文件权限
- [ ] 测试基本功能
- [ ] 检查ZipArchive扩展
- [ ] 配置PHP设置
- [ ] 设置安全措施
- [ ] 创建备份计划
- [ ] 测试所有功能

完成以上步骤后，您就可以开始使用增强版在线文件管理工具了！
