# 增强版在线文件管理工具

基于原版 unzip.php 增强开发，新增了重命名、删除文件和创建文件夹等现代化文件管理功能。

## 🚀 新增功能

### 1. 文件管理操作
- **📁 创建文件夹** - 快速创建新文件夹，支持中文名称
- **✏️ 重命名文件** - 重命名文件和文件夹，包含合法性检查
- **🗑️ 删除文件** - 安全删除文件，支持普通删除和强制删除
- **📤 文件上传** - 支持文件上传，包含详细错误提示

### 2. 解压功能
- **📦 ZIP解压** - 解压ZIP文件，显示进度和详细信息
- **🔍 文件预览** - 显示压缩文件内容和大小信息

### 3. 用户界面
- **🎨 现代化设计** - 响应式界面，支持移动设备
- **📱 移动友好** - 适配手机和平板设备
- **🖼️ 文件图标** - 根据文件类型显示不同图标
- **📊 文件信息** - 显示文件大小和详细信息

## 📋 使用方法

### 安装部署
1. 将 `unzip_enhanced.php` 上传到您的Web服务器
2. 确保服务器支持PHP（建议PHP 7.0+）
3. 通过浏览器访问该PHP文件

### 基本操作

#### 创建文件夹
1. 在"创建文件夹"输入框中输入文件夹名称
2. 点击"创建文件夹"按钮
3. 系统会验证文件夹名称并创建

#### 重命名文件/文件夹
1. 点击文件或文件夹旁的"重命名"按钮
2. 在弹出的对话框中输入新名称
3. 系统会验证名称合法性并执行重命名

#### 删除文件/文件夹
- **普通删除**：点击"删除"按钮（只能删除空文件夹）
- **强制删除**：点击"强删"按钮（可删除非空文件夹，需二次确认）

#### 上传文件
1. 点击"选择文件"按钮选择要上传的文件
2. 点击"上传文件"按钮开始上传
3. 系统会显示上传结果和错误信息

#### 解压文件
1. 在"ZIP文件解压"区域选择要解压的压缩文件
2. 点击"解压文件"按钮
3. 系统会显示解压进度和结果

## 🛡️ 安全特性

### 文件名验证
- 防止使用非法字符（`< > : " / \ | ? *`）
- 确保文件系统兼容性
- 支持中文和特殊字符

### 删除保护
- 普通删除需要用户确认
- 强制删除需要双重确认
- 详细的操作提示和警告

### 错误处理
- 完善的错误提示机制
- 详细的操作反馈
- 文件权限检查

## 📁 文件结构

```
unzip_enhanced.php   # 主程序文件 (需要PHP 7.0+)
unzip_compatible.php # 兼容版本 (支持PHP 5.4+)
demo.html           # 功能演示页面
README.md           # 说明文档
```

## ⚠️ 版本兼容性

### PHP版本要求
- **unzip_enhanced.php**: 需要 PHP 7.0 或更高版本（使用了空合并运算符 `??`）
- **unzip_compatible.php**: 支持 PHP 5.4 或更高版本（兼容性更好）

### 如何选择版本
1. **如果您的服务器运行 PHP 7.0+**: 使用 `unzip_enhanced.php`
2. **如果您的服务器运行较旧的PHP版本**: 使用 `unzip_compatible.php`
3. **不确定PHP版本**: 建议使用 `unzip_compatible.php`（兼容性更好）

### 检查PHP版本
在服务器上创建一个PHP文件，内容如下：
```php
<?php
echo "PHP版本: " . phpversion();
?>
```

## 🔧 技术要求

### 服务器要求
- PHP 7.0 或更高版本
- 支持文件上传（`upload_max_filesize`）
- 支持ZipArchive扩展（用于解压功能）

### 浏览器要求
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 支持JavaScript
- 支持HTML5文件API

## 🎯 功能对比

| 功能 | 原版 unzip.php | 增强版 |
|------|---------------|--------|
| ZIP解压 | ✅ | ✅ |
| 文件列表 | ✅ | ✅ |
| 创建文件夹 | ❌ | ✅ |
| 重命名文件 | ❌ | ✅ |
| 删除文件 | ❌ | ✅ |
| 文件上传 | ❌ | ✅ |
| 现代化界面 | ❌ | ✅ |
| 响应式设计 | ❌ | ✅ |
| 文件图标 | ❌ | ✅ |
| 错误处理 | 基础 | 完善 |

## 📝 更新日志

### v2.0 (增强版)
- ✅ 新增文件夹创建功能
- ✅ 新增文件重命名功能
- ✅ 新增文件删除功能（普通+强制）
- ✅ 新增文件上传功能
- ✅ 全新现代化界面设计
- ✅ 响应式布局支持
- ✅ 文件类型图标显示
- ✅ 完善的错误处理机制
- ✅ 文件大小显示
- ✅ 操作确认和安全检查

### v1.3 (原版)
- ✅ 基础ZIP解压功能
- ✅ 文件列表显示
- ✅ 基于PclZip库

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

基于原版 CMSware Unzipper v1.3 的GNU Lesser General Public License协议。

## ⚠️ 免责声明

本工具仅供学习和开发使用，请确保在安全的环境中使用，并定期备份重要文件。作者不对因使用本工具造成的任何数据丢失或损害承担责任。
