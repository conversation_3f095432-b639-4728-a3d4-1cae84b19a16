<?php
/*
 * PHP版本检测工具
 * 用于确定应该使用哪个版本的增强文件管理工具
 */

$php_version = phpversion();
$php_major = (int)explode('.', $php_version)[0];
$php_minor = (int)explode('.', $php_version)[1];

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP版本检测 - 增强版文件管理工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .version-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .version-number {
            font-size: 48px;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        
        .recommendation {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .recommend-enhanced {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .recommend-compatible {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .feature-comparison {
            margin: 20px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 PHP版本检测</h1>
            <p>确定您应该使用哪个版本的增强文件管理工具</p>
        </div>
        
        <div class="content">
            <div class="version-info">
                <h2>当前PHP版本</h2>
                <div class="version-number"><?php echo $php_version; ?></div>
                <p>检测时间: <?php echo date('Y-m-d H:i:s'); ?></p>
            </div>
            
            <?php if ($php_major >= 7): ?>
                <div class="recommendation recommend-enhanced">
                    <h3>✅ 推荐使用: unzip_enhanced.php</h3>
                    <p>您的PHP版本支持所有现代语法特性，可以使用功能最完整的增强版本。</p>
                    <p><strong>优势:</strong> 更简洁的代码、更好的性能、完整的错误处理</p>
                </div>
            <?php else: ?>
                <div class="recommendation recommend-compatible">
                    <h3>⚠️ 推荐使用: unzip_compatible.php</h3>
                    <p>您的PHP版本较旧，建议使用兼容版本以确保正常运行。</p>
                    <p><strong>说明:</strong> 功能完全相同，只是语法适配了较旧的PHP版本</p>
                </div>
            <?php endif; ?>
            
            <div class="feature-comparison">
                <h3>📊 版本对比</h3>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>特性</th>
                            <th>unzip_enhanced.php</th>
                            <th>unzip_compatible.php</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>PHP版本要求</td>
                            <td>PHP 7.0+</td>
                            <td>PHP 5.4+</td>
                        </tr>
                        <tr>
                            <td>创建文件夹</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>重命名文件</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>删除文件</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>文件上传</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>ZIP解压</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>现代化界面</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>响应式设计</td>
                            <td>✅</td>
                            <td>✅</td>
                        </tr>
                        <tr>
                            <td>代码优化</td>
                            <td>更优</td>
                            <td>兼容性优先</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <h3>📥 下载推荐版本</h3>
                <?php if ($php_major >= 7): ?>
                    <a href="unzip_enhanced.php" class="btn btn-success" download>
                        📦 下载 unzip_enhanced.php (推荐)
                    </a>
                    <a href="unzip_compatible.php" class="btn btn-warning" download>
                        📦 下载 unzip_compatible.php (备用)
                    </a>
                <?php else: ?>
                    <a href="unzip_compatible.php" class="btn btn-success" download>
                        📦 下载 unzip_compatible.php (推荐)
                    </a>
                    <a href="unzip_enhanced.php" class="btn btn-warning" download>
                        📦 下载 unzip_enhanced.php (需升级PHP)
                    </a>
                <?php endif; ?>
            </div>
            
            <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h4>💡 使用提示</h4>
                <ul>
                    <li><strong>安全提醒:</strong> 请在使用前备份重要文件</li>
                    <li><strong>权限设置:</strong> 确保PHP有读写当前目录的权限</li>
                    <li><strong>文件大小:</strong> 注意服务器的文件上传大小限制</li>
                    <li><strong>扩展支持:</strong> ZIP解压需要ZipArchive扩展支持</li>
                </ul>
            </div>
            
            <div style="text-align: center; color: #6c757d; margin-top: 30px;">
                <p>基于原版 CMSware Unzipper v1.3 增强开发</p>
                <p>增加了现代化的文件管理功能和用户界面</p>
            </div>
        </div>
    </div>
</body>
</html>
