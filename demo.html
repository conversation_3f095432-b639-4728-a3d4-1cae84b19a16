<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版在线文件管理工具 - 功能演示</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #6c757d;
            line-height: 1.5;
        }
        
        .demo-section {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .demo-title {
            color: #0056b3;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-list li {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ 增强版在线文件管理工具</h1>
            <p>基于原版 unzip.php 增强，新增重命名、删除文件和创建文件夹功能</p>
        </div>
        
        <div class="content">
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">📁</div>
                    <div class="feature-title">创建文件夹</div>
                    <div class="feature-desc">快速创建新文件夹，支持中文名称和特殊字符验证</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">✏️</div>
                    <div class="feature-title">重命名文件</div>
                    <div class="feature-desc">重命名文件和文件夹，包含文件名合法性检查</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🗑️</div>
                    <div class="feature-title">删除文件</div>
                    <div class="feature-desc">安全删除文件，支持普通删除和强制删除两种模式</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📤</div>
                    <div class="feature-title">文件上传</div>
                    <div class="feature-desc">支持文件上传，包含详细的错误提示和状态反馈</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📦</div>
                    <div class="feature-title">ZIP解压</div>
                    <div class="feature-desc">解压ZIP文件，显示进度条和详细的解压信息</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">现代界面</div>
                    <div class="feature-desc">响应式设计，美观的用户界面，支持移动设备</div>
                </div>
            </div>
            
            <div class="demo-section">
                <div class="demo-title">🚀 新增功能详解</div>
                
                <h3>1. 文件管理操作</h3>
                <ul class="step-list">
                    <li>
                        <span class="step-number">1</span>
                        <strong>创建文件夹：</strong>在操作面板输入文件夹名称，点击"创建文件夹"按钮
                    </li>
                    <li>
                        <span class="step-number">2</span>
                        <strong>重命名：</strong>点击文件或文件夹旁的"重命名"按钮，输入新名称
                    </li>
                    <li>
                        <span class="step-number">3</span>
                        <strong>删除：</strong>点击"删除"按钮删除空文件夹或文件，点击"强删"按钮强制删除非空文件夹
                    </li>
                    <li>
                        <span class="step-number">4</span>
                        <strong>上传：</strong>选择文件后点击"上传文件"按钮，支持各种文件类型
                    </li>
                </ul>
                
                <h3>2. 安全特性</h3>
                <ul class="step-list">
                    <li>
                        <span class="step-number">🛡️</span>
                        <strong>文件名验证：</strong>防止使用非法字符，确保文件系统兼容性
                    </li>
                    <li>
                        <span class="step-number">⚠️</span>
                        <strong>删除确认：</strong>多重确认机制，防止误删重要文件
                    </li>
                    <li>
                        <span class="step-number">📝</span>
                        <strong>操作日志：</strong>详细的操作反馈和错误提示
                    </li>
                    <li>
                        <span class="step-number">🔒</span>
                        <strong>权限检查：</strong>检查文件权限，确保操作安全性
                    </li>
                </ul>
            </div>
            
            <div class="highlight">
                <h3>📋 使用说明</h3>
                <p><strong>1. 部署文件：</strong>将 <code>unzip_enhanced.php</code> 上传到您的Web服务器</p>
                <p><strong>2. 访问页面：</strong>通过浏览器访问该PHP文件</p>
                <p><strong>3. 开始使用：</strong>即可享受完整的文件管理功能</p>
            </div>
            
            <div class="demo-section">
                <div class="demo-title">💡 技术特点</div>
                <div class="code-block">
                    <strong>新增功能：</strong><br>
                    ✅ 文件重命名 (rename)<br>
                    ✅ 文件删除 (delete/unlink)<br>
                    ✅ 文件夹创建 (mkdir)<br>
                    ✅ 文件夹递归删除 (rmdir recursive)<br>
                    ✅ 文件上传 (upload)<br>
                    ✅ 现代化UI界面<br>
                    ✅ 响应式设计<br>
                    ✅ 错误处理和用户反馈<br>
                    ✅ 文件类型图标显示<br>
                    ✅ 文件大小显示
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <p style="color: #6c757d;">基于原版 CMSware Unzipper v1.3 增强开发</p>
                <p style="color: #6c757d;">增加了现代化的文件管理功能和用户界面</p>
            </div>
        </div>
    </div>
</body>
</html>
