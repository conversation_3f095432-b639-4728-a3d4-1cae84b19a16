<?php
/**
 * 一键修复空白页面问题
 * 自动诊断并修复美化后的空白页面问题
 */

// 错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查是否在正确的目录中
if (!file_exists('template/form/show.htm')) {
    die('错误：请在插件根目录中运行此脚本');
}

/**
 * 修复函数
 */
function fixBlankPage() {
    $fixes = [];
    $errors = [];
    
    // 1. 备份当前文件
    $backup_dir = 'backup_fix_' . date('Y-m-d_H-i-s');
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    if (file_exists('template/form/show.htm')) {
        copy('template/form/show.htm', $backup_dir . '/show.htm');
        $fixes[] = "已备份当前模板到 $backup_dir";
    }
    
    // 2. 使用简化版现代化模板或创建一个
    $simple_template_path = 'template/form/show_simple_modern.htm';

    if (file_exists($simple_template_path)) {
        copy($simple_template_path, 'template/form/show.htm');
        $fixes[] = "已应用简化版现代化模板";
    } else {
        // 创建一个基础的现代化模板
        $basic_template = createBasicModernTemplate();
        file_put_contents('template/form/show.htm', $basic_template);
        $fixes[] = "已创建并应用基础现代化模板";
    }
    
    // 3. 检查并创建必要的CSS文件
    if (!file_exists('css/hejin-forms-modern.css')) {
        // 创建基础的现代化CSS
        $basic_css = '
/* 基础现代化样式 */
:root {
    --primary-color: #409EFF;
    --success-color: #67C23A;
    --text-regular: #606266;
    --border-base: #DCDFE6;
}

.hf-form-wrapper {
    max-width: 800px;
    margin: 20px auto;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.hf-form-container {
    background: white;
    padding: 32px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.field {
    margin-bottom: 20px;
}

.hf-input-base {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-base);
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.hf-input-base:focus {
    border-color: var(--primary-color);
    outline: none;
}

.hf-btn-primary {
    background: var(--primary-color);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}
';
        
        if (!is_dir('css')) {
            mkdir('css', 0755, true);
        }
        
        file_put_contents('css/hejin-forms-modern.css', $basic_css);
        $fixes[] = "已创建基础现代化CSS文件";
    }
    
    // 4. 检查并创建必要的JS文件
    if (!file_exists('js/hejin-forms-async-compatible.js')) {
        // 创建基础的兼容JS
        $basic_js = '
// 基础兼容性脚本
document.addEventListener("DOMContentLoaded", function() {
    console.log("和金表单已加载");
    
    // 基础表单增强
    var form = document.querySelector("#new_entry");
    if (form) {
        // 添加基础样式类
        form.classList.add("hf-form-container");
        
        // 为输入框添加样式类
        var inputs = form.querySelectorAll("input, textarea, select");
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].type !== "hidden" && inputs[i].type !== "submit") {
                inputs[i].classList.add("hf-input-base");
            }
        }
        
        // 为提交按钮添加样式类
        var submitBtn = form.querySelector("input[type=submit]");
        if (submitBtn) {
            submitBtn.classList.add("hf-btn", "hf-btn-primary");
        }
    }
});
';
        
        if (!is_dir('js')) {
            mkdir('js', 0755, true);
        }
        
        file_put_contents('js/hejin-forms-async-compatible.js', $basic_js);
        $fixes[] = "已创建基础兼容JS文件";
    }
    
    // 5. 检查文件权限
    $dirs = ['css', 'js', 'template', 'template/form'];
    foreach ($dirs as $dir) {
        if (is_dir($dir) && !is_writable($dir)) {
            chmod($dir, 0755);
            $fixes[] = "已修复目录权限: $dir";
        }
    }
    
    return ['fixes' => $fixes, 'errors' => $errors, 'backup' => $backup_dir];
}

// 处理修复请求
if (isset($_GET['action']) && $_GET['action'] === 'fix') {
    $result = fixBlankPage();
    
    echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>修复结果</title></head><body>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}h1,h2{color:#333;}.success{color:#28a745;background:#d4edda;padding:10px;border-radius:5px;margin:10px 0;}.error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:5px;margin:10px 0;}.info{color:#0c5460;background:#d1ecf1;padding:10px;border-radius:5px;margin:10px 0;}.btn{background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:3px;display:inline-block;margin:10px 5px;}</style>";
    
    echo "<h1>🔧 修复结果</h1>";
    
    if (!empty($result['fixes'])) {
        echo "<div class='success'>";
        echo "<h3>✅ 修复完成</h3>";
        echo "<ul>";
        foreach ($result['fixes'] as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($result['errors'])) {
        echo "<div class='error'>";
        echo "<h3>❌ 发现错误</h3>";
        echo "<ul>";
        foreach ($result['errors'] as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h3>📋 下一步操作</h3>";
    echo "<ol>";
    echo "<li>清除浏览器缓存</li>";
    echo "<li>刷新表单页面</li>";
    echo "<li>如果仍有问题，请查看浏览器控制台错误</li>";
    echo "<li>备份文件位置：{$result['backup']}</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<a href='?' class='btn'>返回诊断页面</a>";
    echo "<a href='debug_modern_style.php' class='btn'>打开详细调试</a>";
    echo "</body></html>";
    exit;
}

// 显示诊断页面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空白页面一键修复工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            color: #303133;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 32px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
            padding: 24px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            border-radius: 8px;
            color: white;
        }
        
        .header h1 {
            margin: 0 0 12px 0;
            font-size: 28px;
        }
        
        .section {
            margin: 24px 0;
            padding: 20px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
        }
        
        .diagnostic {
            background: #F0F9FF;
            border-color: #409EFF;
        }
        
        .warning {
            background: #FFFBF0;
            border-color: #E6A23C;
            color: #E6A23C;
        }
        
        .success {
            background: #F0F9F0;
            border-color: #67C23A;
            color: #67C23A;
        }
        
        .error {
            background: #FEF0F0;
            border-color: #F56C6C;
            color: #F56C6C;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #409EFF;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #337ecc;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: #F56C6C;
        }
        
        .btn-danger:hover {
            background: #dd6161;
        }
        
        .btn-success {
            background: #67C23A;
        }
        
        .btn-success:hover {
            background: #529b2e;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 20px 0;
        }
        
        .status-item {
            padding: 16px;
            border-radius: 6px;
            text-align: center;
        }
        
        .status-ok {
            background: #F0F9F0;
            color: #67C23A;
            border: 1px solid #67C23A;
        }
        
        .status-error {
            background: #FEF0F0;
            color: #F56C6C;
            border: 1px solid #F56C6C;
        }
        
        .status-warning {
            background: #FFFBF0;
            color: #E6A23C;
            border: 1px solid #E6A23C;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #EBEEF5;
        }
        
        .checklist li:last-child {
            border-bottom: none;
        }
        
        .check-ok::before {
            content: "✅ ";
        }
        
        .check-error::before {
            content: "❌ ";
        }
        
        .check-warning::before {
            content: "⚠️ ";
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 空白页面一键修复工具</h1>
            <p>自动诊断并修复美化后的空白页面问题</p>
        </div>
        
        <div class="section diagnostic">
            <h2>🔍 问题诊断</h2>
            <p>正在检查可能导致空白页面的问题...</p>
            
            <div class="status-grid">
                <?php
                // 快速诊断
                $checks = [
                    'template' => file_exists('template/form/show.htm'),
                    'modern_css' => file_exists('css/hejin-forms-modern.css'),
                    'modern_js' => file_exists('js/hejin-forms-modern.js'),
                    'async_js' => file_exists('js/hejin-forms-async-compatible.js'),
                    'simple_template' => file_exists('template/form/show_simple_modern.htm')
                ];
                
                $labels = [
                    'template' => '当前模板文件',
                    'modern_css' => '现代化CSS',
                    'modern_js' => '现代化JS',
                    'async_js' => '异步兼容JS',
                    'simple_template' => '简化模板'
                ];
                
                foreach ($checks as $key => $status) {
                    $class = $status ? 'status-ok' : 'status-error';
                    $icon = $status ? '✅' : '❌';
                    echo "<div class='status-item $class'>";
                    echo "<div>$icon</div>";
                    echo "<div>{$labels[$key]}</div>";
                    echo "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="section warning">
            <h2>⚠️ 常见问题原因</h2>
            <ul class="checklist">
                <li class="check-error">CSS或JS文件路径错误</li>
                <li class="check-error">模板语法错误</li>
                <li class="check-error">文件权限问题</li>
                <li class="check-error">浏览器缓存问题</li>
                <li class="check-error">PHP语法错误</li>
                <li class="check-error">文件编码问题</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🛠️ 修复方案</h2>
            <p>点击下面的按钮执行自动修复：</p>
            
            <div style="text-align: center; margin: 24px 0;">
                <a href="?action=fix" class="btn btn-success" style="font-size: 16px; padding: 16px 32px;">
                    🚀 一键自动修复
                </a>
            </div>
            
            <h3>修复内容包括：</h3>
            <ul>
                <li>✅ 自动备份当前文件</li>
                <li>✅ 应用简化版现代化模板</li>
                <li>✅ 创建必要的CSS和JS文件</li>
                <li>✅ 修复文件权限问题</li>
                <li>✅ 确保基础功能正常</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🔧 手动修复选项</h2>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <a href="debug_modern_style.php" class="btn">详细调试工具</a>
                <a href="style_switcher.php" class="btn">样式切换器</a>
                <a href="test_compatibility.html" class="btn">兼容性测试</a>
                <a href="test_form.html" class="btn">表单测试</a>
            </div>
        </div>
        
        <div class="section">
            <h2>📋 修复后检查清单</h2>
            <ul class="checklist">
                <li>🔄 清除浏览器缓存</li>
                <li>🌐 在不同浏览器中测试</li>
                <li>📱 检查移动端显示</li>
                <li>🔍 查看浏览器控制台错误</li>
                <li>📊 测试表单提交功能</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 32px; color: #909399;">
            <p>💡 如果问题仍然存在，请联系技术支持并提供浏览器控制台错误信息</p>
        </div>
    </div>
</body>
</html>
