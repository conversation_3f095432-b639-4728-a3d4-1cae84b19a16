<?php
/**
 * PHP兼容性检查和修复工具
 * 修复PHP版本兼容性问题
 */

// 基础错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>PHP兼容性修复工具</title>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;}
.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.success{background:#d4edda;color:#155724;padding:15px;border-radius:5px;margin:10px 0;}
.error{background:#f8d7da;color:#721c24;padding:15px;border-radius:5px;margin:10px 0;}
.warning{background:#fff3cd;color:#856404;padding:15px;border-radius:5px;margin:10px 0;}
.info{background:#d1ecf1;color:#0c5460;padding:15px;border-radius:5px;margin:10px 0;}
h1,h2,h3{color:#333;}
.btn{background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:3px;display:inline-block;margin:5px;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{padding:8px;text-align:left;border-bottom:1px solid #ddd;}
th{background:#f8f9fa;}
.code{background:#f8f9fa;padding:10px;border-radius:3px;font-family:monospace;font-size:12px;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 PHP兼容性修复工具</h1>";

// 检查PHP版本
echo "<div class='info'>";
echo "<h2>📋 PHP环境信息</h2>";
echo "<table>";
echo "<tr><th>项目</th><th>值</th><th>状态</th></tr>";
echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td><td>" . (version_compare(PHP_VERSION, '5.6.0', '>=') ? '<span style=\"color:green;\">✓ 支持</span>' : '<span style=\"color:red;\">✗ 过低</span>') . "</td></tr>";
echo "<tr><td>支持null合并操作符(??)</td><td>" . (version_compare(PHP_VERSION, '7.0.0', '>=') ? '是' : '否') . "</td><td>" . (version_compare(PHP_VERSION, '7.0.0', '>=') ? '<span style=\"color:green;\">✓</span>' : '<span style=\"color:orange;\">需要兼容处理</span>') . "</td></tr>";
echo "<tr><td>支持类型声明</td><td>" . (version_compare(PHP_VERSION, '7.0.0', '>=') ? '是' : '否') . "</td><td>" . (version_compare(PHP_VERSION, '7.0.0', '>=') ? '<span style=\"color:green;\">✓</span>' : '<span style=\"color:orange;\">需要兼容处理</span>') . "</td></tr>";
echo "<tr><td>支持匿名类</td><td>" . (version_compare(PHP_VERSION, '7.0.0', '>=') ? '是' : '否') . "</td><td>" . (version_compare(PHP_VERSION, '7.0.0', '>=') ? '<span style=\"color:green;\">✓</span>' : '<span style=\"color:orange;\">需要兼容处理</span>') . "</td></tr>";
echo "</table>";
echo "</div>";

// 检查文件兼容性
$files_to_check = [
    'style_switcher.php',
    'fix_blank_page.php',
    'debug_modern_style.php',
    'api.inc.php',
    'js/hejin-forms-async-compatible.js',
    'js/hejin-forms-modern.js'
];

echo "<div class='info'>";
echo "<h2>📁 文件兼容性检查</h2>";

$issues_found = false;

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $file_issues = [];
        
        // 检查null合并操作符
        if (strpos($content, '??') !== false && version_compare(PHP_VERSION, '7.0.0', '<')) {
            $file_issues[] = '使用了null合并操作符(??)，需要PHP 7.0+';
        }
        
        // 检查类型声明
        if (preg_match('/function\s+\w+\s*\([^)]*:\s*\w+/', $content) && version_compare(PHP_VERSION, '7.0.0', '<')) {
            $file_issues[] = '使用了返回类型声明，需要PHP 7.0+';
        }
        
        // 检查短数组语法
        if (strpos($content, '[]') !== false && version_compare(PHP_VERSION, '5.4.0', '<')) {
            $file_issues[] = '使用了短数组语法([])，需要PHP 5.4+';
        }
        
        if (!empty($file_issues)) {
            $issues_found = true;
            echo "<div class='warning'>";
            echo "<h4>⚠️ $file</h4>";
            echo "<ul>";
            foreach ($file_issues as $issue) {
                echo "<li>$issue</li>";
            }
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='success'>";
            echo "<p>✓ $file - 兼容性检查通过</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<p>✗ $file - 文件不存在</p>";
        echo "</div>";
    }
}

if (!$issues_found) {
    echo "<div class='success'>";
    echo "<h3>🎉 所有文件兼容性检查通过！</h3>";
    echo "</div>";
}

echo "</div>";

// 自动修复功能
if (isset($_GET['action']) && $_GET['action'] === 'fix') {
    echo "<div class='info'>";
    echo "<h2>🔧 自动修复结果</h2>";
    
    $fixes_applied = [];
    
    // 修复style_switcher.php中的null合并操作符
    if (file_exists('style_switcher.php')) {
        $content = file_get_contents('style_switcher.php');
        $original_content = $content;
        
        // 替换 ?? 操作符
        $content = preg_replace('/\$(\w+)\s*=\s*\$_POST\[\'(\w+)\'\]\s*\?\?\s*\'([^\']*)\';/', '$\1 = isset($_POST[\'\2\']) ? $_POST[\'\2\'] : \'\3\';', $content);
        $content = preg_replace('/\$(\w+)\s*=\s*\$_GET\[\'(\w+)\'\]\s*\?\?\s*\'([^\']*)\';/', '$\1 = isset($_GET[\'\2\']) ? $_GET[\'\2\'] : \'\3\';', $content);
        
        if ($content !== $original_content) {
            file_put_contents('style_switcher.php', $content);
            $fixes_applied[] = 'style_switcher.php - 修复null合并操作符';
        }
    }
    
    if (!empty($fixes_applied)) {
        echo "<div class='success'>";
        echo "<h4>✅ 修复完成</h4>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='info'>";
        echo "<p>没有发现需要修复的兼容性问题。</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// 手动修复指南
echo "<div class='warning'>";
echo "<h2>🛠️ 手动修复指南</h2>";

if (version_compare(PHP_VERSION, '7.0.0', '<')) {
    echo "<h3>PHP 5.6 兼容性修复</h3>";
    echo "<h4>1. null合并操作符(??)替换：</h4>";
    echo "<div class='code'>";
    echo "// 原代码（PHP 7.0+）<br>";
    echo "\$value = \$_POST['key'] ?? 'default';<br><br>";
    echo "// 兼容代码（PHP 5.6+）<br>";
    echo "\$value = isset(\$_POST['key']) ? \$_POST['key'] : 'default';";
    echo "</div>";
    
    echo "<h4>2. 类型声明移除：</h4>";
    echo "<div class='code'>";
    echo "// 原代码（PHP 7.0+）<br>";
    echo "function test(): string { return 'hello'; }<br><br>";
    echo "// 兼容代码（PHP 5.6+）<br>";
    echo "function test() { return 'hello'; }";
    echo "</div>";
}

echo "<h3>常见兼容性问题解决方案：</h3>";
echo "<ol>";
echo "<li><strong>语法错误：</strong> 检查PHP版本特定语法</li>";
echo "<li><strong>函数不存在：</strong> 添加函数存在性检查</li>";
echo "<li><strong>类不存在：</strong> 添加类存在性检查</li>";
echo "<li><strong>扩展缺失：</strong> 检查必要的PHP扩展</li>";
echo "</ol>";

echo "</div>";

// 操作按钮
echo "<div class='info'>";
echo "<h2>🚀 操作选项</h2>";
echo "<a href='?action=fix' class='btn'>自动修复兼容性问题</a>";
echo "<a href='fix_blank_page.php' class='btn'>返回空白页面修复</a>";
echo "<a href='style_switcher.php' class='btn'>测试样式切换器</a>";
echo "</div>";

// 建议升级
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    echo "<div class='warning'>";
    echo "<h2>💡 建议</h2>";
    echo "<p>您当前使用的PHP版本是 " . PHP_VERSION . "，建议升级到PHP 7.4或更高版本以获得：</p>";
    echo "<ul>";
    echo "<li>更好的性能</li>";
    echo "<li>更多的语言特性</li>";
    echo "<li>更好的安全性</li>";
    echo "<li>更好的错误处理</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>"; // container
echo "</body></html>";
?>
