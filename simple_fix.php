<?php
/**
 * 简单修复工具 - 兼容PHP 5.6+
 * 修复空白页面问题的简化版本
 */

// 基础错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查是否在正确的目录中
if (!file_exists('template/form/show.htm')) {
    die('错误：请在插件根目录中运行此脚本');
}

/**
 * 创建基础模板
 */
function createBasicTemplate() {
    $template = '<!--{template header}-->

<!-- 基础样式 -->
<style>
.form-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    font-family: Arial, sans-serif;
}

.form-title {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
}

.form-description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
}

.field {
    margin-bottom: 20px;
}

.control-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-input:focus {
    border-color: #409EFF;
    outline: none;
}

.submit-btn {
    background: #409EFF;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    display: block;
    margin: 30px auto 0;
}

.submit-btn:hover {
    background: #337ecc;
}

.help-text {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.required {
    color: red;
}
</style>

<div class="form-container">
    <h1 class="form-title">{$form[\'name\']}</h1>
    <div class="form-description">{$form[\'description\']}</div>
    
    <form action="" id="new_entry" method="post" enctype="multipart/form-data">
        <input type="hidden" name="formhash" value="{FORMHASH}" />
        <input type="hidden" name="fid" value="{$form[\'id\']}" />
        <input type="hidden" name="uid" value="{$_G[\'uid\']}" />
        <input type="hidden" name="account" value="{$_G[\'username\']}" />

        <!--{loop $groups $key $val}-->
        <!--{eval $config = fix_json($val[\'config\']);}-->
        
        <div class="field">
            <label class="control-label" for="G-{$val[\'id\']}">
                {$val[\'name\']}
                <!--{if $config[\'GROUP_MUST\']}-->
                <span class="required">*</span>
                <!--{/if}-->
            </label>
            
            <!--{if $val[\'description\']}-->
            <div class="help-text">{$val[\'description\']}</div>
            <!--{/if}-->
            
            <!--{if $val[\'type\'] == "text"}-->
                <input name="G-{$val[id]}" id="G-{$val[id]}" class="form-input" type="text" value="" 
                       fix_name="{$val[name]}" <!--{if $config[GROUP_MUST]}-->required<!--{/if}--> 
                       placeholder="请输入{$val[name]}" />
                       
            <!--{elseif $val[\'type\'] == "textarea"}-->
                <textarea name="G-{$val[id]}" id="G-{$val[id]}" class="form-input" 
                          rows="6" fix_name="{$val[name]}" <!--{if $config[GROUP_MUST]}-->required<!--{/if}-->
                          placeholder="请输入{$val[name]}"></textarea>
                          
            <!--{elseif $val[\'type\'] == "file"}-->
                <input name="G-{$val[id]}" id="G-{$val[id]}" type="file" class="form-input"
                       fix_name="{$val[name]}" <!--{if $config[GROUP_MUST]}-->required<!--{/if}--> />
                       
            <!--{elseif $val[\'type\'] == "select"}-->
                <select id="G-{$val[id]}" name="G-{$val[id]}" class="form-input" 
                        fix_name="{$val[name]}" <!--{if $config[GROUP_MUST]}-->required<!--{/if}-->>
                    <option value="">请选择{$val[name]}</option>
                    <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                    <!--{loop $options $keyi $vala}-->
                    <option value="{$vala[\'name\']}">{$vala[\'name\']}</option>
                    <!--{/loop}-->
                </select>
                
            <!--{elseif $val[\'type\'] == "radio"}-->
                <div>
                    <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                    <!--{loop $options $keyi $vala}-->
                    <label style="display: inline-block; margin-right: 15px;">
                        <input type="radio" name="G-{$val[id]}" value="{$vala[\'name\']}" 
                               fix_name="{$val[name]}" <!--{if $config[GROUP_MUST]}-->required<!--{/if}--> />
                        {$vala[\'name\']}
                    </label>
                    <!--{/loop}-->
                </div>
                
            <!--{elseif $val[\'type\'] == "checkbox"}-->
                <div>
                    <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                    <!--{loop $options $keyi $vala}-->
                    <label style="display: block; margin-bottom: 5px;">
                        <input type="checkbox" name="G-{$val[id]}[]" value="{$vala[\'name\']}" 
                               fix_name="{$val[name]}" />
                        {$vala[\'name\']}
                    </label>
                    <!--{/loop}-->
                </div>
                
            <!--{/if}-->
        </div>
        
        <!--{/loop}-->
        
        <input class="submit-btn" name="commit" value="提交表单" type="submit">
    </form>
</div>

<!--{template footer}-->';

    return $template;
}

/**
 * 执行修复
 */
function performFix() {
    $results = array();
    
    // 1. 备份当前文件
    $backup_dir = 'backup_simple_' . date('Y-m-d_H-i-s');
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    if (file_exists('template/form/show.htm')) {
        copy('template/form/show.htm', $backup_dir . '/show.htm');
        $results[] = array('type' => 'success', 'message' => '已备份当前模板到 ' . $backup_dir);
    }
    
    // 2. 创建新的基础模板
    $template_content = createBasicTemplate();
    file_put_contents('template/form/show.htm', $template_content);
    $results[] = array('type' => 'success', 'message' => '已创建基础模板文件');
    
    // 3. 检查目录权限
    $dirs = array('template', 'template/form', 'css', 'js');
    foreach ($dirs as $dir) {
        if (is_dir($dir) && !is_writable($dir)) {
            chmod($dir, 0755);
            $results[] = array('type' => 'success', 'message' => '已修复目录权限: ' . $dir);
        }
    }
    
    return $results;
}

// 处理修复请求
if (isset($_GET['action']) && $_GET['action'] === 'fix') {
    $results = performFix();
    
    echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>修复完成</title></head><body>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:600px;margin:50px auto;padding:20px;}h1{color:#333;text-align:center;}.result{padding:10px;margin:10px 0;border-radius:5px;}.success{background:#d4edda;color:#155724;border:1px solid #c3e6cb;}.error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}.btn{background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:3px;display:inline-block;margin:10px 5px;}</style>";
    
    echo "<h1>🔧 修复完成</h1>";
    
    foreach ($results as $result) {
        echo "<div class='result " . $result['type'] . "'>";
        echo ($result['type'] === 'success' ? '✅ ' : '❌ ') . $result['message'];
        echo "</div>";
    }
    
    echo "<div style='text-align:center;margin-top:30px;'>";
    echo "<h3>📋 下一步操作</h3>";
    echo "<ol style='text-align:left;'>";
    echo "<li>清除浏览器缓存（Ctrl+F5）</li>";
    echo "<li>刷新表单页面</li>";
    echo "<li>测试表单功能</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='text-align:center;'>";
    echo "<a href='?' class='btn'>返回</a>";
    echo "</div>";
    
    echo "</body></html>";
    exit;
}

// 显示主页面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单修复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        
        .btn {
            background: #28a745;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
            margin: 20px 10px;
        }
        
        .btn:hover {
            background: #218838;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        
        .status-ok {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 简单修复工具</h1>
        <p>兼容PHP 5.6+的空白页面修复工具</p>
        
        <div class="info">
            <h3>📋 当前环境</h3>
            <p><strong>PHP版本：</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>兼容性：</strong> 
                <?php if (version_compare(PHP_VERSION, '5.6.0', '>=')): ?>
                    <span style="color: green;">✅ 支持</span>
                <?php else: ?>
                    <span style="color: red;">❌ 版本过低</span>
                <?php endif; ?>
            </p>
        </div>
        
        <div class="warning">
            <h3>⚠️ 修复说明</h3>
            <ul>
                <li>此工具会自动备份当前模板文件</li>
                <li>创建一个简化的基础模板</li>
                <li>修复常见的权限问题</li>
                <li>兼容PHP 5.6及以上版本</li>
            </ul>
        </div>
        
        <div class="status">
            <?php
            $template_exists = file_exists('template/form/show.htm');
            if ($template_exists) {
                echo '<div class="status-ok">✅ 模板文件存在</div>';
            } else {
                echo '<div class="status-error">❌ 模板文件不存在</div>';
            }
            ?>
        </div>
        
        <a href="?action=fix" class="btn">🚀 开始修复</a>
        <a href="php_compatibility_fix.php" class="btn btn-secondary">检查PHP兼容性</a>
        
        <div style="margin-top: 30px; font-size: 12px; color: #666;">
            <p>💡 如果修复后仍有问题，请检查浏览器控制台错误信息</p>
        </div>
    </div>
</body>
</html>
