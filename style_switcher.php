<?php
/**
 * 和金表单样式切换工具
 * 在原版样式和现代化Element UI样式之间切换
 */

// 检查是否在正确的目录中
if (!file_exists('template/form/show.htm')) {
    die('错误：请在插件根目录中运行此脚本');
}

// 样式配置
$styles = [
    'original' => [
        'name' => '原版样式',
        'description' => '保持原有的经典样式，简洁实用',
        'template' => 'template/form/show.htm',
        'css' => ['css/hejin-forms-async.css'],
        'js' => ['js/hejin-forms-async-compatible.js'],
        'preview' => 'preview_original.jpg'
    ],
    'modern' => [
        'name' => '现代化样式',
        'description' => 'Element UI风格，扁平化设计，丰富的动画效果',
        'template' => 'template/form/show_modern.htm',
        'css' => ['css/hejin-forms-modern.css', 'css/hejin-forms-async.css'],
        'js' => ['js/hejin-forms-async-compatible.js', 'js/hejin-forms-modern.js'],
        'preview' => 'preview_modern.jpg'
    ]
];

/**
 * 获取当前样式
 */
function getCurrentStyle() {
    $currentTemplate = 'template/form/show.htm';
    if (file_exists($currentTemplate)) {
        $content = file_get_contents($currentTemplate);
        if (strpos($content, 'hejin-forms-modern.css') !== false) {
            return 'modern';
        }
    }
    return 'original';
}

/**
 * 切换样式
 */
function switchStyle($targetStyle, $styles) {
    if (!isset($styles[$targetStyle])) {
        return ['success' => false, 'message' => '无效的样式选择'];
    }
    
    $config = $styles[$targetStyle];
    $templateFile = 'template/form/show.htm';
    
    // 备份当前模板
    $backupDir = 'backup_style_' . date('Y-m-d_H-i-s');
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    if (file_exists($templateFile)) {
        copy($templateFile, $backupDir . '/show.htm');
    }
    
    // 复制新模板
    if ($targetStyle === 'modern') {
        if (file_exists('template/form/show_modern.htm')) {
            copy('template/form/show_modern.htm', $templateFile);
        } else {
            return ['success' => false, 'message' => '现代化模板文件不存在'];
        }
    } else {
        // 恢复原版模板（如果有备份的话）
        $originalBackup = null;
        $backupDirs = glob('backup_style_*');
        foreach ($backupDirs as $dir) {
            $backupFile = $dir . '/show.htm';
            if (file_exists($backupFile)) {
                $content = file_get_contents($backupFile);
                if (strpos($content, 'hejin-forms-modern.css') === false) {
                    $originalBackup = $backupFile;
                    break;
                }
            }
        }
        
        if ($originalBackup) {
            copy($originalBackup, $templateFile);
        } else {
            return ['success' => false, 'message' => '找不到原版模板备份'];
        }
    }
    
    return ['success' => true, 'message' => '样式切换成功', 'backup' => $backupDir];
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $targetStyle = isset($_POST['style']) ? $_POST['style'] : '';
    $result = switchStyle($targetStyle, $styles);
    
    echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>样式切换结果</title></head><body>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}h1,h2,h3{color:#333;}.success{color:green;}.error{color:red;}.btn{background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:3px;display:inline-block;margin:10px 5px;}</style>";
    
    echo "<h1>样式切换结果</h1>";
    
    if ($result['success']) {
        echo "<p class='success'>✓ {$result['message']}</p>";
        echo "<p>备份目录：{$result['backup']}</p>";
        echo "<p>请清除浏览器缓存后查看效果。</p>";
    } else {
        echo "<p class='error'>✗ {$result['message']}</p>";
    }
    
    echo "<a href='?' class='btn'>返回设置</a>";
    echo "</body></html>";
    exit;
}

$currentStyle = getCurrentStyle();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和金表单样式切换工具</title>
    <style>
        :root {
            --primary-color: #409EFF;
            --success-color: #67C23A;
            --warning-color: #E6A23C;
            --danger-color: #F56C6C;
            --text-primary: #303133;
            --text-regular: #606266;
            --text-secondary: #909399;
            --border-base: #DCDFE6;
            --background-white: #FFFFFF;
            --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            --border-radius-base: 4px;
            --border-radius-large: 6px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            color: var(--text-primary);
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--success-color));
            border-radius: var(--border-radius-large);
            color: white;
            box-shadow: var(--box-shadow-light);
        }
        
        .header h1 {
            margin: 0 0 16px 0;
            font-size: 32px;
            font-weight: 600;
        }
        
        .header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .current-style {
            background: var(--background-white);
            padding: 24px;
            border-radius: var(--border-radius-base);
            margin-bottom: 32px;
            box-shadow: var(--box-shadow-light);
            text-align: center;
        }
        
        .current-style h2 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
        }
        
        .current-badge {
            display: inline-block;
            padding: 6px 16px;
            background: var(--success-color);
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .styles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .style-card {
            background: var(--background-white);
            border-radius: var(--border-radius-large);
            overflow: hidden;
            box-shadow: var(--box-shadow-light);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .style-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .style-card.current {
            border: 2px solid var(--success-color);
        }
        
        .style-card.current::before {
            content: '当前使用';
            position: absolute;
            top: 16px;
            right: 16px;
            background: var(--success-color);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1;
        }
        
        .style-preview {
            height: 200px;
            background: linear-gradient(45deg, #f0f2f5, #e4e7ed);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: var(--text-secondary);
        }
        
        .style-info {
            padding: 24px;
        }
        
        .style-name {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: var(--text-primary);
        }
        
        .style-description {
            color: var(--text-regular);
            margin: 0 0 16px 0;
            line-height: 1.5;
        }
        
        .style-features {
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
        }
        
        .style-features li {
            padding: 4px 0;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .style-features li::before {
            content: '✓';
            color: var(--success-color);
            font-weight: bold;
            margin-right: 8px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius-base);
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #337ecc;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }
        
        .warning {
            background: rgba(230, 162, 60, 0.1);
            color: var(--warning-color);
            padding: 16px;
            border-radius: var(--border-radius-base);
            border-left: 4px solid var(--warning-color);
            margin-bottom: 24px;
        }
        
        .warning h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        
        .warning ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .comparison {
            background: var(--background-white);
            padding: 24px;
            border-radius: var(--border-radius-large);
            box-shadow: var(--box-shadow-light);
            margin-bottom: 32px;
        }
        
        .comparison h2 {
            text-align: center;
            margin-bottom: 24px;
            color: var(--text-primary);
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-base);
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .comparison-table td {
            color: var(--text-regular);
        }
        
        .check {
            color: var(--success-color);
            font-weight: bold;
        }
        
        .cross {
            color: var(--text-secondary);
        }
        
        @media (max-width: 768px) {
            .styles-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 24px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .comparison-table {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 和金表单样式切换工具</h1>
            <p>选择您喜欢的表单样式，让表单更加美观和现代化</p>
        </div>
        
        <div class="current-style">
            <h2>当前样式</h2>
            <span class="current-badge"><?php echo $styles[$currentStyle]['name']; ?></span>
        </div>
        
        <div class="warning">
            <h3>⚠️ 切换前请注意</h3>
            <ul>
                <li>切换样式会自动备份当前模板文件</li>
                <li>建议先在测试环境中预览效果</li>
                <li>切换后请清除浏览器缓存</li>
                <li>如有问题可从备份目录恢复</li>
            </ul>
        </div>
        
        <div class="styles-grid">
            <?php foreach ($styles as $key => $style): ?>
            <div class="style-card <?php echo $currentStyle === $key ? 'current' : ''; ?>">
                <div class="style-preview">
                    <?php echo $key === 'modern' ? '🎨' : '📝'; ?>
                </div>
                <div class="style-info">
                    <h3 class="style-name"><?php echo $style['name']; ?></h3>
                    <p class="style-description"><?php echo $style['description']; ?></p>
                    
                    <ul class="style-features">
                        <?php if ($key === 'original'): ?>
                        <li>经典简洁的设计风格</li>
                        <li>兼容性好，加载速度快</li>
                        <li>适合传统业务场景</li>
                        <li>文件体积小</li>
                        <?php else: ?>
                        <li>Element UI扁平化设计</li>
                        <li>丰富的动画和交互效果</li>
                        <li>现代化的视觉体验</li>
                        <li>响应式设计，移动端友好</li>
                        <li>进度指示器和实时反馈</li>
                        <li>深色模式支持</li>
                        <?php endif; ?>
                    </ul>
                    
                    <form method="post" style="margin: 0;">
                        <input type="hidden" name="style" value="<?php echo $key; ?>">
                        <button type="submit" class="btn" <?php echo $currentStyle === $key ? 'disabled' : ''; ?>>
                            <?php echo $currentStyle === $key ? '当前样式' : '切换到此样式'; ?>
                        </button>
                    </form>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="comparison">
            <h2>样式对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能特性</th>
                        <th>原版样式</th>
                        <th>现代化样式</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>基础表单功能</td>
                        <td><span class="check">✓</span></td>
                        <td><span class="check">✓</span></td>
                    </tr>
                    <tr>
                        <td>异步提交</td>
                        <td><span class="check">✓</span></td>
                        <td><span class="check">✓</span></td>
                    </tr>
                    <tr>
                        <td>实时验证</td>
                        <td><span class="check">✓</span></td>
                        <td><span class="check">✓</span></td>
                    </tr>
                    <tr>
                        <td>文件上传</td>
                        <td><span class="check">✓</span></td>
                        <td><span class="check">✓ 增强</span></td>
                    </tr>
                    <tr>
                        <td>动画效果</td>
                        <td><span class="cross">基础</span></td>
                        <td><span class="check">✓ 丰富</span></td>
                    </tr>
                    <tr>
                        <td>进度指示器</td>
                        <td><span class="cross">-</span></td>
                        <td><span class="check">✓</span></td>
                    </tr>
                    <tr>
                        <td>浮动标签</td>
                        <td><span class="cross">-</span></td>
                        <td><span class="check">✓</span></td>
                    </tr>
                    <tr>
                        <td>拖拽上传</td>
                        <td><span class="check">✓</span></td>
                        <td><span class="check">✓ 增强</span></td>
                    </tr>
                    <tr>
                        <td>深色模式</td>
                        <td><span class="cross">-</span></td>
                        <td><span class="check">✓</span></td>
                    </tr>
                    <tr>
                        <td>文件大小</td>
                        <td><span class="check">小</span></td>
                        <td><span class="cross">较大</span></td>
                    </tr>
                    <tr>
                        <td>加载速度</td>
                        <td><span class="check">快</span></td>
                        <td><span class="cross">稍慢</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div style="text-align: center; margin-top: 40px; color: var(--text-secondary);">
            <p>💡 提示：可以随时切换样式，所有数据和功能都会保持不变</p>
        </div>
    </div>
</body>
</html>
