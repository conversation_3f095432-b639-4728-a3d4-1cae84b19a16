<?php header('Content-type: text/html; charset=UTF-8'); ?>
<?php session_start(); ?>
<?php if (file_exists('pass.php')) include 'pass.php'; ?>
<?php

// 处理文件管理操作
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    $target = isset($_POST['target']) ? $_POST['target'] : '';
    $newname = isset($_POST['newname']) ? $_POST['newname'] : '';
    
    switch ($action) {
        case 'rename':
            if ($target && $newname && file_exists($target)) {
                $dir = dirname($target);
                $newpath = ($dir === '.' ? '' : $dir . '/') . $newname;
                if (rename($target, $newpath)) {
                    echo "<div style='color: green; margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;'>✅ 重命名成功: " . htmlspecialchars($target) . " → " . htmlspecialchars($newname) . "</div>";
                } else {
                    echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 重命名失败</div>";
                }
            }
            break;
            
        case 'delete':
            if ($target && file_exists($target)) {
                if (is_dir($target)) {
                    if (rmdir($target)) {
                        echo "<div style='color: green; margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;'>✅ 文件夹删除成功: " . htmlspecialchars($target) . "</div>";
                    } else {
                        echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 文件夹删除失败（请确保文件夹为空）</div>";
                    }
                } else {
                    if (unlink($target)) {
                        echo "<div style='color: green; margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;'>✅ 文件删除成功: " . htmlspecialchars($target) . "</div>";
                    } else {
                        echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 文件删除失败</div>";
                    }
                }
            }
            break;
            
        case 'mkdir':
            if ($newname) {
                $newdir = './' . $newname;
                if (!file_exists($newdir)) {
                    if (mkdir($newdir, 0755)) {
                        echo "<div style='color: green; margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;'>✅ 文件夹创建成功: " . htmlspecialchars($newname) . "</div>";
                    } else {
                        echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 文件夹创建失败</div>";
                    }
                } else {
                    echo "<div style='color: orange; margin: 10px 0; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;'>⚠️ 文件夹已存在: " . htmlspecialchars($newname) . "</div>";
                }
            }
            break;
            
        case 'delete_force':
            if ($target && file_exists($target)) {
                if (deleteRecursive($target)) {
                    echo "<div style='color: green; margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;'>✅ 强制删除成功: " . htmlspecialchars($target) . "</div>";
                } else {
                    echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 强制删除失败</div>";
                }
            }
            break;
    }
}

// 处理文件上传
if (isset($_POST['upload']) && isset($_FILES['upload_file'])) {
    $upload_file = $_FILES['upload_file'];
    if ($upload_file['error'] === UPLOAD_ERR_OK) {
        $filename = basename($upload_file['name']);
        $target_path = './' . $filename;
        
        if (move_uploaded_file($upload_file['tmp_name'], $target_path)) {
            echo "<div style='color: green; margin: 10px 0; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px;'>✅ 文件上传成功: " . htmlspecialchars($filename) . "</div>";
        } else {
            echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 文件上传失败</div>";
        }
    } else {
        $error_messages = array(
            UPLOAD_ERR_INI_SIZE => '文件大小超过了 php.ini 中 upload_max_filesize 的限制',
            UPLOAD_ERR_FORM_SIZE => '文件大小超过了表单中 MAX_FILE_SIZE 的限制',
            UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
            UPLOAD_ERR_NO_FILE => '没有文件被上传',
            UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
            UPLOAD_ERR_CANT_WRITE => '文件写入失败',
            UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
        );
        $error_msg = isset($error_messages[$upload_file['error']]) ? $error_messages[$upload_file['error']] : '未知错误';
        echo "<div style='color: red; margin: 10px 0; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;'>❌ 上传失败: " . $error_msg . "</div>";
    }
}

// 递归删除函数
function deleteRecursive($path) {
    if (is_dir($path)) {
        $files = array_diff(scandir($path), array('.', '..'));
        foreach ($files as $file) {
            deleteRecursive($path . '/' . $file);
        }
        return rmdir($path);
    } else {
        return unlink($path);
    }
}

// 格式化文件大小
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

/***************************************************************************
                             Enhanced Unzipper with File Management
 ***************************************************************************
    Enhanced version with rename, delete, and create folder functionality
    Compatible with PHP 5.4+
    Based on original Unzipper v1.3
***************************************************************************/
?>

<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>增强版在线解压工具</title>
    <style type="text/css">
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .content {
            padding: 20px;
        }
        
        .file-manager {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .file-manager h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .create-folder {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .create-folder input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .file-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .file-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .file-section-header {
            background: #007bff;
            color: white;
            padding: 15px;
            font-weight: bold;
            text-align: center;
        }
        
        .file-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }
        
        .file-item:hover {
            background-color: #f8f9fa;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .file-icon {
            font-size: 18px;
            margin-right: 10px;
        }
        
        .file-name {
            font-weight: 500;
            color: #495057;
        }
        
        .file-size {
            font-size: 12px;
            color: #6c757d;
            margin-left: 10px;
        }
        
        .file-actions {
            display: flex;
            gap: 5px;
        }
        
        .operation-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .operation-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 16px;
        }
        
        .file-operations {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .file-operations .operation-card {
            flex: 1;
            min-width: 200px;
        }
        
        @media (max-width: 768px) {
            .file-list {
                grid-template-columns: 1fr;
            }
            
            .create-folder {
                flex-direction: column;
                align-items: stretch;
            }
            
            .file-operations {
                flex-direction: column;
            }
            
            .operation-card {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ 增强版在线文件管理工具</h1>
            <p>支持解压、重命名、删除文件和创建文件夹 (兼容PHP 5.4+)</p>
        </div>
        
        <div class="content">
            <!-- 文件管理操作界面 -->
            <div class="file-operations">
                <div class="operation-card">
                    <h4>📁 创建文件夹</h4>
                    <form method="post" class="create-folder">
                        <input type="hidden" name="action" value="mkdir">
                        <input type="text" name="newname" placeholder="输入新文件夹名称..." required>
                        <button type="submit" class="btn btn-success">创建文件夹</button>
                    </form>
                </div>

                <div class="operation-card">
                    <h4>📤 上传文件</h4>
                    <form method="post" enctype="multipart/form-data" class="create-folder">
                        <input type="file" name="upload_file" style="flex: 1; padding: 10px; border: 1px solid #ced4da; border-radius: 4px;" required>
                        <button type="submit" name="upload" class="btn btn-primary">上传文件</button>
                    </form>
                </div>
            </div>

            <!-- 文件列表 -->
            <div class="file-list">
                <div class="file-section">
                    <div class="file-section-header">📁 文件夹</div>
                    <?php
                    $current_dir = './';
                    if ($handle = opendir($current_dir)) {
                        $has_dirs = false;
                        while (false !== ($entry = readdir($handle))) {
                            if ($entry != "." && $entry != ".." && is_dir($current_dir . $entry)) {
                                $has_dirs = true;
                                echo "<div class='file-item'>";
                                echo "<div class='file-info'>";
                                echo "<span class='file-icon'>📁</span>";
                                echo "<span class='file-name'>" . htmlspecialchars($entry) . "</span>";
                                echo "</div>";
                                echo "<div class='file-actions'>";
                                echo "<button onclick='renameItem(\"" . htmlspecialchars($entry) . "\")' class='btn btn-warning btn-sm'>重命名</button>";
                                echo "<button onclick='deleteItem(\"" . htmlspecialchars($entry) . "\", true)' class='btn btn-danger btn-sm'>删除</button>";
                                echo "<button onclick='forceDeleteItem(\"" . htmlspecialchars($entry) . "\")' class='btn btn-danger btn-sm' style='background: #dc3545; opacity: 0.8;' title='强制删除（包括非空文件夹）'>强删</button>";
                                echo "</div>";
                                echo "</div>";
                            }
                        }
                        if (!$has_dirs) {
                            echo "<div class='file-item'><span style='color: #6c757d; font-style: italic;'>暂无文件夹</span></div>";
                        }
                        closedir($handle);
                    }
                    ?>
                </div>

                <div class="file-section">
                    <div class="file-section-header">📄 文件</div>
                    <?php
                    if ($handle = opendir($current_dir)) {
                        $has_files = false;
                        while (false !== ($entry = readdir($handle))) {
                            if ($entry != "." && $entry != ".." && is_file($current_dir . $entry)) {
                                $has_files = true;
                                $filesize = filesize($current_dir . $entry);
                                $ext = strtolower(pathinfo($entry, PATHINFO_EXTENSION));

                                // 根据文件类型显示不同图标
                                $icon = "📄";
                                if (in_array($ext, array('zip', 'rar', '7z', 'tar', 'gz'))) $icon = "📦";
                                elseif (in_array($ext, array('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'))) $icon = "🖼️";
                                elseif (in_array($ext, array('txt', 'log', 'md'))) $icon = "📝";
                                elseif (in_array($ext, array('php', 'html', 'css', 'js', 'json'))) $icon = "💻";
                                elseif (in_array($ext, array('pdf'))) $icon = "📕";
                                elseif (in_array($ext, array('doc', 'docx'))) $icon = "📘";
                                elseif (in_array($ext, array('xls', 'xlsx'))) $icon = "📗";
                                elseif (in_array($ext, array('mp3', 'wav', 'flac'))) $icon = "🎵";
                                elseif (in_array($ext, array('mp4', 'avi', 'mkv'))) $icon = "🎬";

                                echo "<div class='file-item'>";
                                echo "<div class='file-info'>";
                                echo "<span class='file-icon'>$icon</span>";
                                echo "<span class='file-name'>" . htmlspecialchars($entry) . "</span>";
                                echo "<span class='file-size'>(" . formatBytes($filesize) . ")</span>";
                                echo "</div>";
                                echo "<div class='file-actions'>";
                                echo "<button onclick='renameItem(\"" . htmlspecialchars($entry) . "\")' class='btn btn-warning btn-sm'>重命名</button>";
                                echo "<button onclick='deleteItem(\"" . htmlspecialchars($entry) . "\", false)' class='btn btn-danger btn-sm'>删除</button>";
                                echo "</div>";
                                echo "</div>";
                            }
                        }
                        if (!$has_files) {
                            echo "<div class='file-item'><span style='color: #6c757d; font-style: italic;'>暂无文件</span></div>";
                        }
                        closedir($handle);
                    }
                    ?>
                </div>
            </div>

            <!-- 解压功能 -->
            <div class="operation-card" style="width: 100%; margin-bottom: 20px;">
                <h4>📦 ZIP文件解压</h4>

                <?php
                // 显示ZIP文件列表
                $zip_files = array();
                if ($handle = opendir('./')) {
                    while (false !== ($entry = readdir($handle))) {
                        if ($entry != "." && $entry != ".." && is_file('./' . $entry)) {
                            $ext = strtolower(pathinfo($entry, PATHINFO_EXTENSION));
                            if (in_array($ext, array('zip', 'rar', '7z'))) {
                                $zip_files[] = $entry;
                            }
                        }
                    }
                    closedir($handle);
                }

                if (!empty($zip_files)) {
                    echo "<form method='post'>";
                    echo "<div class='create-folder'>";
                    echo "<select name='zip_file' style='flex: 1; padding: 10px; border: 1px solid #ced4da; border-radius: 4px;'>";
                    echo "<option value=''>选择要解压的压缩文件...</option>";
                    foreach ($zip_files as $zip_file) {
                        $filesize = formatBytes(filesize($zip_file));
                        echo "<option value='" . htmlspecialchars($zip_file) . "'>" . htmlspecialchars($zip_file) . " ($filesize)</option>";
                    }
                    echo "</select>";
                    echo "<button type='submit' name='extract_zip' class='btn btn-primary'>🗜️ 解压文件</button>";
                    echo "</div>";
                    echo "</form>";
                } else {
                    echo "<p style='color: #6c757d; font-style: italic; text-align: center; padding: 20px;'>📦 当前目录下没有压缩文件</p>";
                }

                // 处理解压操作
                if (isset($_POST['extract_zip']) && !empty($_POST['zip_file'])) {
                    $zip_file = $_POST['zip_file'];
                    if (file_exists($zip_file)) {
                        echo "<div style='margin-top: 15px; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 8px;'>";
                        echo "<h4 style='margin: 0 0 10px 0; color: #0056b3;'>🔄 解压进度</h4>";
                        echo "<p>正在解压文件: <strong>" . htmlspecialchars($zip_file) . "</strong></p>";

                        // 使用PHP内置的ZipArchive类进行解压
                        if (class_exists('ZipArchive')) {
                            $zip = new ZipArchive;
                            $res = $zip->open($zip_file);
                            if ($res === TRUE) {
                                $total_files = $zip->numFiles;
                                echo "<p>发现 $total_files 个文件，开始解压...</p>";

                                $zip->extractTo('./');
                                $zip->close();
                                echo "<p style='color: green; font-weight: bold; margin-top: 10px;'>✅ 解压成功！共解压 $total_files 个文件</p>";
                            } else {
                                echo "<p style='color: red; font-weight: bold;'>❌ 解压失败，错误代码: $res</p>";
                            }
                        } else {
                            echo "<p style='color: red; font-weight: bold;'>❌ 服务器不支持ZipArchive扩展，无法解压文件</p>";
                        }
                        echo "</div>";
                    }
                }
                ?>
            </div>
        </div>
    </div>

    <script>
        function renameItem(filename) {
            var newName = prompt('请输入新名称:', filename);
            if (newName && newName !== filename && newName.replace(/^\s+|\s+$/g, '') !== '') {
                // 验证文件名 (兼容旧版浏览器)
                var invalidChars = /[<>:"/\\|?*]/;
                if (invalidChars.test(newName)) {
                    alert('文件名包含非法字符！请避免使用 < > : " / \\ | ? *');
                    return;
                }

                var form = document.createElement('form');
                form.method = 'post';
                form.innerHTML =
                    '<input type="hidden" name="action" value="rename">' +
                    '<input type="hidden" name="target" value="' + filename + '">' +
                    '<input type="hidden" name="newname" value="' + newName + '">';
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteItem(filename, isDir) {
            var message = isDir ?
                '确定要删除文件夹 "' + filename + '" 吗？\n\n⚠️ 注意：只能删除空文件夹，如需删除非空文件夹请使用"强删"功能' :
                '确定要删除文件 "' + filename + '" 吗？\n\n此操作不可恢复！';

            if (confirm(message)) {
                var form = document.createElement('form');
                form.method = 'post';
                form.innerHTML =
                    '<input type="hidden" name="action" value="delete">' +
                    '<input type="hidden" name="target" value="' + filename + '">';
                document.body.appendChild(form);
                form.submit();
            }
        }

        function forceDeleteItem(filename) {
            var message = '⚠️ 危险操作警告！\n\n确定要强制删除 "' + filename + '" 吗？\n\n这将删除文件夹及其所有内容，此操作不可恢复！\n\n请再次确认您要执行此操作。';

            if (confirm(message)) {
                var secondConfirm = confirm('最后确认：真的要强制删除 "' + filename + '" 吗？');
                if (secondConfirm) {
                    var form = document.createElement('form');
                    form.method = 'post';
                    form.innerHTML =
                        '<input type="hidden" name="action" value="delete_force">' +
                        '<input type="hidden" name="target" value="' + filename + '">';
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }

        // 自动刷新页面以显示最新的文件列表
        function refreshPage() {
            location.reload();
        }
    </script>
</body>
</html>
